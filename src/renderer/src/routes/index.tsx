import appLogoImg from "../assets/images/app-logo.png";
import introPosterImg from "../assets/images/intro-poster.jpg";

import introSound1 from "../assets/sounds/1intro.mp3";
import introSound2 from "../assets/sounds/2intro.mp3";
import introSound3 from "../assets/sounds/3intro.mp3";
import countdownSound from "../assets/sounds/countdownAudio.mp3";

import introVideo from "../assets/videos/shuffule.mp4";

import { motion } from "motion/react";

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/dialog";

import { useEffect, useMemo, useRef, useState } from "react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";

import { cn } from "@renderer/lib/utils";
import { validateMachine } from "@renderer/lib/machineValidator";

// Import Bingo components
import { GameInfoBar } from "../components/GameInfoBar";
import { BallDrawingArea } from "../components/BallDrawingArea";
import { BingoNumberGrid } from "../components/BingoNumberGrid";
import { WinningPattern } from "../components/WinningPattern";
import { checkBoard, getPatternGrid } from "../lib/boardUtils";
import {
  getBoardByIndex,
  getTotalBoardCount,
  isValidBoardIndex,
  getRandomBoardIndex,
} from "../lib/cartelaGenerator";

// Import Bingo types
import type {
  BingoGameStatus,
  BingoGameHistory,
  WinningPatternType,
  DrawingPhase,
  CheckBoardResult,
} from "../types/bingo";
import { Button } from "@renderer/components/button";

// Utility functions - removed generateGameCode as it's now server-generated

interface BingoNumberGridProps {
  drawnNumbers: number[];
  lastDrawnNumber?: number;
  className?: string;
  onNumbersChange?: (numbers: number[]) => void;
}

const plateVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.01,
    },
  },
};

const numberEntryVariants = {
  hidden: {
    scale: 0.5,
    opacity: 0,
  },
  visible: {
    scale: 1,
    opacity: 1,
  },
};

export const Route = createFileRoute("/")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();

  // Load initial state from localStorage
  const loadFromLocalStorage = () => {
    try {
      const savedPatternGrid = localStorage.getItem("bingoPatternGrid");
      const savedSelectedNumbers = localStorage.getItem("bingoSelectedNumbers");
      const savedMaxWin2 = localStorage.getItem("bingoMaxWin2");
      const savedBlockedBoards = localStorage.getItem("bingoBlockedBoards");

      return {
        patternGrid: savedPatternGrid
          ? JSON.parse(savedPatternGrid)
          : [
              ["", "", "", "", ""],
              ["", "", "", "", ""],
              ["", "", "", "", ""],
              ["", "", "", "", ""],
              ["", "", "", "", ""],
            ],
        selectedNumbers: savedSelectedNumbers
          ? JSON.parse(savedSelectedNumbers)
          : [],
        maxWin2: savedMaxWin2 ? JSON.parse(savedMaxWin2) : "",
        blockedBoards: savedBlockedBoards
          ? new Set(JSON.parse(savedBlockedBoards))
          : new Set(),
      };
    } catch (error) {
      console.error("Error loading from localStorage:", error);
      return {
        patternGrid: [
          ["", "", "", "", ""],
          ["", "", "", "", ""],
          ["", "", "", "", ""],
          ["", "", "", "", ""],
          ["", "", "", "", ""],
        ],
        selectedNumbers: [],
        maxWin2: "",
        blockedBoards: new Set(),
      };
    }
  };

  // Initialize state with localStorage values
  const initialState = loadFromLocalStorage();

  // Machine validation state
  const [isMachineValid, setIsMachineValid] = useState<boolean | null>(null);
  const [machineValidationError, setMachineValidationError] = useState<
    string | null
  >(null);

  // Bingo game state
  const [gameHistory, setGameHistory] = useState<BingoGameHistory[]>([]);
  const [showGameHistory, setShowGameHistory] = useState(false);
  const [gameStatus, setGameStatus] = useState<BingoGameStatus | undefined>(
    undefined,
  );
  const [countdown, setCountdown] = useState<number | undefined>(undefined);

  // Drawing state
  const [allDrawnNumbers, setAllDrawnNumbers] = useState<number[]>([]);
  const [drawnNumbers, setDrawnNumbers] = useState<number[]>(
    JSON.parse(sessionStorage.getItem("recentBingoNumbers") || "[]")?.at(0)
      ?.drawnNumbers || [],
  );
  const [currentBall, setCurrentBall] = useState<number>();
  const [drawingPhase, setDrawingPhase] = useState<DrawingPhase>("waiting");

  // Game configuration - all dynamic from server
  const [winningPattern, setWinningPattern] = useState<WinningPatternType>("");
  const [round, setRound] = useState<number>(
    JSON.parse(sessionStorage.getItem("recentBingoNumbers") || "[]")?.at(0)
      ?.round || 1,
  );
  const [gameCode, setGameCode] = useState<string>(
    JSON.parse(sessionStorage.getItem("recentBingoNumbers") || "[]")?.at(0)
      ?.gameCode || "0000",
  );

  // UI state
  const [showIntroVideo, setShowIntroVideo] = useState(false);
  const [isIntroVideoLoaded, setIsIntroVideoLoaded] = useState(false);
  const [isBetClosed, setIsBetClosed] = useState(false);
  const [isEventOffline, setIsEventOffline] = useState(false);
  const [offlineEventCount, setOfflineEventCount] = useState(0);
  // Board checking state
  const [boardId, setBoardId] = useState<string>("");
  const [isCheckingBoard, setIsCheckingBoard] = useState(false);
  const [checkResult, setCheckResult] = useState<CheckBoardResult | null>(null);
  const [blockedBoards, setBlockedBoards] = useState<Set<number>>(
    initialState.blockedBoards,
  );

  // Game state with localStorage persistence
  const [patternGrid, setPatternGrid] = useState(initialState.patternGrid);
  const [selectedNumbers, setSelectedNumbers] = useState<number[]>(
    initialState.selectedNumbers,
  );
  const [maxWin2, setMaxWin2] = useState<string>(initialState.maxWin2);

  // Audio refs
  const introAudio1Ref = useRef<HTMLAudioElement>(null);
  const introAudio2Ref = useRef<HTMLAudioElement>(null);
  const introAudio3Ref = useRef<HTMLAudioElement>(null);
  const countdownSoundRef = useRef<HTMLAudioElement>(null);

  // Delayed drawing state for animations
  const [delayedDrawnNumbers, setDelayedDrawnNumbers] = useState<number[]>([]);
  const [delayedCurrentBall, setDelayedCurrentBall] = useState<
    number | undefined
  >(undefined);

  // Save to localStorage functions
  const saveToLocalStorage = {
    patternGrid: (grid: string[][]) => {
      try {
        localStorage.setItem("bingoPatternGrid", JSON.stringify(grid));
      } catch (error) {
        console.error("Error saving pattern grid to localStorage:", error);
      }
    },
    selectedNumbers: (numbers: number[]) => {
      try {
        localStorage.setItem("bingoSelectedNumbers", JSON.stringify(numbers));
      } catch (error) {
        console.error("Error saving selected numbers to localStorage:", error);
      }
    },
    maxWin2: (value: string) => {
      try {
        localStorage.setItem("bingoMaxWin2", JSON.stringify(value));
      } catch (error) {
        console.error("Error saving maxWin2 to localStorage:", error);
      }
    },
    blockedBoards: (boards: Set<number>) => {
      try {
        localStorage.setItem(
          "bingoBlockedBoards",
          JSON.stringify(Array.from(boards)),
        );
      } catch (error) {
        console.error("Error saving blocked boards to localStorage:", error);
      }
    },
    clearAll: () => {
      try {
        localStorage.removeItem("bingoPatternGrid");
        localStorage.removeItem("bingoSelectedNumbers");
        localStorage.removeItem("bingoMaxWin2");
        localStorage.removeItem("bingoBlockedBoards");
      } catch (error) {
        console.error("Error clearing localStorage:", error);
      }
    },
  };

  // Effects to save state to localStorage when it changes
  useEffect(() => {
    saveToLocalStorage.patternGrid(patternGrid);
  }, [patternGrid]);

  useEffect(() => {
    saveToLocalStorage.selectedNumbers(selectedNumbers);
  }, [selectedNumbers]);

  useEffect(() => {
    saveToLocalStorage.maxWin2(maxWin2);
  }, [maxWin2]);

  useEffect(() => {
    saveToLocalStorage.blockedBoards(blockedBoards);
  }, [blockedBoards]);

  console.log("DelatedDrawnNumbers", delayedDrawnNumbers);
  // Effect to update delayed drawn numbers with animation delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedDrawnNumbers([...drawnNumbers]);
    }, 200); // 200ms delay for smooth animations

    return () => clearTimeout(timer);
  }, [drawnNumbers]);

  // Effect to update delayed current ball
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedCurrentBall(currentBall);
    }, 500); // 500ms delay for ball animation

    return () => clearTimeout(timer);
  }, [currentBall]);

  // Machine validation effect
  useEffect(() => {
    const validateMachineOnStartup = async () => {
      try {
        console.log("🔍 Validating machine...");
        const isValid = await validateMachine();
        setIsMachineValid(isValid);

        if (!isValid) {
          setMachineValidationError(
            "This machine is not authorized to run this application",
          );
          console.log("❌ Machine validation failed");
        } else {
          console.log("✅ Machine validation successful");
        }
      } catch (error) {
        console.error("Machine validation error:", error);
        setIsMachineValid(false);
        setMachineValidationError(
          error instanceof Error ? error.message : "Validation error",
        );
      }
    };

    validateMachineOnStartup();
  }, []);

  // Effect for checking if there is a connection and gamestatus
  useEffect(() => {
    if (gameStatus === undefined) {
      navigate({ to: "/" });
    }
  }, [gameStatus]);

  // Effect for checking if the game is offline for too long
  useEffect(() => {
    if (offlineEventCount === 15) {
      navigate({ to: "/" });
    }
  }, [offlineEventCount]);

  // Bingo game history effect
  useEffect(() => {
    if (drawnNumbers.length >= 15 && drawingPhase === "complete") {
      const timer = setTimeout(() => {
        setGameHistory(
          JSON.parse(sessionStorage.getItem("recentBingoNumbers") || "[]"),
        );
        setShowGameHistory(true);
        setDrawingPhase("waiting");

        if (introAudio3Ref.current) {
          introAudio3Ref.current.play();
        }

        clearTimeout(timer);
      }, 5000);
    }

    if (showGameHistory) {
      const timer = setTimeout(() => {
        setShowGameHistory(false);
        setGameHistory([]);

        if (introAudio3Ref.current) {
          introAudio3Ref.current.pause();
          introAudio3Ref.current.currentTime = 0;
        }

        if (introAudio1Ref.current) {
          introAudio1Ref.current.play();
        }

        clearTimeout(timer);
      }, 15000);
    }
  }, [drawnNumbers.length, drawingPhase, showGameHistory]);

  // Countdown timer effect
  useEffect(() => {
    if (!gameStatus?.endTime) {
      setCountdown(0);
      return;
    }

    const timer = setInterval(() => {
      const endTimestamp = new Date(gameStatus.endTime).getTime();
      const remaining = Math.max(
        0,
        Math.floor((endTimestamp - Date.now()) / 1000),
      );
      setCountdown(remaining);

      if (remaining <= 0) {
        clearInterval(timer);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [gameStatus?.endTime]);

  // Sound effects for countdown and game sound
  useEffect(() => {
    const stopSound = (audioRef: React.RefObject<HTMLAudioElement | null>) => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };

    const canPlaySound =
      gameStatus !== undefined &&
      countdown !== undefined &&
      drawingPhase === "waiting" &&
      !showIntroVideo &&
      !showGameHistory &&
      !isBetClosed;

    if (!canPlaySound) {
      stopSound(introAudio1Ref);
      stopSound(introAudio2Ref);
      stopSound(countdownSoundRef);
      return;
    }

    if (countdown < 5) {
      // State: Final countdown
      stopSound(introAudio1Ref);
      if (countdownSoundRef.current) {
        countdownSoundRef.current.play();
      }
    } else if (countdown <= 45) {
      // State: Mid-game countdown music
      stopSound(introAudio1Ref);
      stopSound(countdownSoundRef);
      if (introAudio2Ref.current) {
        introAudio2Ref.current.play();
      }
    } else {
      // State: Early-game intro music (countdown > 45)
      stopSound(introAudio2Ref);
      stopSound(countdownSoundRef);
      if (introAudio1Ref.current) {
        introAudio1Ref.current.play();
      }
    }
  }, [
    gameStatus,
    countdown,
    drawingPhase,
    showIntroVideo,
    showGameHistory,
    isBetClosed,
  ]);

  // Checking if bet is closed
  useEffect(() => {
    if (countdown === undefined) return;

    const minutes = Math.floor(countdown / 60);
    const seconds = countdown % 60;

    if (gameStatus !== undefined && minutes === 0 && seconds === 1) {
      const timer = setTimeout(() => {
        setIsBetClosed(true);
        if (introAudio2Ref.current) {
          introAudio2Ref.current.pause();
          introAudio2Ref.current.currentTime = 0;
        }

        clearTimeout(timer);
      }, 2000);
    }
  }, [countdown]);

  // Bingo ball drawing effect
  useEffect(() => {
    if (drawingPhase === "drawing" && allDrawnNumbers.length > 0) {
      const drawNextBall = () => {
        const nextIndex = drawnNumbers.length;
        if (nextIndex < allDrawnNumbers.length) {
          const nextBall = allDrawnNumbers[nextIndex];
          setCurrentBall(nextBall);

          setTimeout(() => {
            setDrawnNumbers((prev) => [...prev, nextBall]);
          }, 1000);

          setTimeout(() => {
            if (nextIndex + 1 >= allDrawnNumbers.length) {
              setDrawingPhase("complete");
            } else {
              drawNextBall();
            }
          }, 2500);
        }
      };

      drawNextBall();
    }
  }, [drawingPhase, allDrawnNumbers, drawnNumbers.length]);

  // Manual draw function for testing

  // Get recent balls for the drawing area
  const recentBalls = useMemo(() => {
    return delayedDrawnNumbers.slice(-8); // Show last 8 balls around the circle
  }, [delayedDrawnNumbers]);

  // Test function to manually trigger drawing
  const startDrawing = () => {
    if (drawingPhase === "waiting") {
      setDrawingPhase("drawing");
    }
  };

  const handleLeftClick = (row: number, col: number) => {
    // if (!selectedNumbers.length) {
    const newGrid = [...patternGrid];
    newGrid[row][col] = newGrid[row][col] === "yellow" ? "" : "yellow";
    setPatternGrid(newGrid);
    // }
  };

  const handleRightClick = (e: React.MouseEvent, row: number, col: number) => {
    e.preventDefault();
    // if (!selectedNumbers.length) {
    const newGrid = [...patternGrid];
    newGrid[row][col] = newGrid[row][col] === "green" ? "" : "green";
    setPatternGrid(newGrid);
    // }
  };

  const getBallColor = (number: number) => {
    if (number >= 1 && number <= 15) return "bg-blue-500"; // B
    if (number >= 16 && number <= 30) return "bg-purple-500"; // I
    if (number >= 31 && number <= 45) return "bg-red-500"; // N
    if (number >= 46 && number <= 60) return "bg-green-500"; // G
    if (number >= 61 && number <= 75) return "bg-yellow-500"; // O
    return "bg-gray-500";
  };

  const getBallLetter = (number: number) => {
    if (number >= 1 && number <= 15) return "B";
    if (number >= 16 && number <= 30) return "I";
    if (number >= 31 && number <= 45) return "N";
    if (number >= 46 && number <= 60) return "G";
    if (number >= 61 && number <= 75) return "O";
    return "";
  };

  const handleClick = (num: number) => {
    const newSelectedNumbers = selectedNumbers.includes(num)
      ? selectedNumbers.filter((existing) => existing !== num)
      : [...selectedNumbers, num];

    setSelectedNumbers(newSelectedNumbers);
    onNumbersChange?.(newSelectedNumbers);
  };
  const drawnSet = useMemo(() => new Set(drawnNumbers), [drawnNumbers]);
  const selectedSet = useMemo(
    () => new Set(selectedNumbers),
    [selectedNumbers],
  );
  const rows = 5;
  const cols = 15;

  // BINGO letters for each row
  const bingoLetters = ["B", "I", "N", "G", "O"];
  const bingoColors = [
    { letter: "B", textColor: "yellow-300", color: "text-blue-400", bg: "bg-blue-900/50" },
    { letter: "I", textColor: "grey-300", color: "text-purple-400", bg: "bg-purple-900/50" },
    { letter: "N", textColor: "green", color: "text-red-400", bg: "bg-red-900/50" },
    { letter: "G", textColor: "black", color: "text-green-400", bg: "bg-green-900/50" },
    { letter: "O", textColor: "blue", color: "text-yellow-400", bg: "bg-yellow-900/50" },
  ];

  const handleMaxwin = (value: any) => {
    setMaxWin2(value.target.value);
  };

  // Board checking function
  const handleCheckBoard = async () => {
    const boardIdNum = parseInt(boardId);

    if (!isValidBoardIndex(boardIdNum)) {
      setCheckResult({
        boardId: boardIdNum || 0,
        board: [],
        isValid: false,
        hasWon: false,
        markedNumbers: [],
        error: `Invalid board ID. Must be between 0 and ${getTotalBoardCount() - 1}`,
      });
      return;
    }

    // Check if board is blocked
    if (blockedBoards.has(boardIdNum)) {
      setCheckResult({
        boardId: boardIdNum,
        board: [],
        isValid: false,
        hasWon: false,
        markedNumbers: [],
        error: `Board ${boardIdNum} has been blocked and cannot be checked again.`,
      });
      return;
    }

    setIsCheckingBoard(true);
    try {
      const result = checkBoard(
        parseInt(boardId),
        selectedNumbers,
        winningPattern,
      );
      setCheckResult(result);
    } catch (error) {
      setCheckResult({
        boardId: parseInt(boardId) || 0,
        board: [],
        isValid: false,
        hasWon: false,
        markedNumbers: [],
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
    setIsCheckingBoard(false);
  };

  // Block board function
  const handleBlockBoard = () => {
    if (checkResult && checkResult.isValid) {
      setIsBlockDialogOpen(true);
    }
  };

  // Confirm block function
  const handleBlockConfirm = () => {
    if (checkResult && checkResult.isValid) {
      const newBlockedBoards = new Set(blockedBoards);
      newBlockedBoards.add(checkResult.boardId);
      setBlockedBoards(newBlockedBoards);
      setCheckResult(null);
      setBoardId("");
      setIsBlockDialogOpen(false);
    }
  };

  // Handle Enter key for board checking
  const handleBoardIdKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (
        isValidBoardIndex(parseInt(boardId)) &&
        !isCheckingBoard &&
        !blockedBoards.has(parseInt(boardId))
      ) {
        handleCheckBoard();
      }
    }
  };

  const [isClearDialogOpen, setIsClearDialogOpen] = useState(false);
  const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false);

  // Handle keyboard events for block dialog
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isBlockDialogOpen) return;

      if (event.key === "Enter") {
        event.preventDefault();
        handleBlockConfirm();
      } else if (event.key === "Escape") {
        event.preventDefault();
        setIsBlockDialogOpen(false);
      }
    };

    if (isBlockDialogOpen) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isBlockDialogOpen, checkResult]);

  const handleClearConfirm = () => {
    // Clear all state
    setSelectedNumbers([]);
    setMaxWin2("");
    setBoardId("");
    setCheckResult(null);
    setBlockedBoards(new Set()); // Reset blocked boards on game restart
    setIsClearDialogOpen(false);
    setPatternGrid([
      ["", "", "", "", ""],
      ["", "", "", "", ""],
      ["", "", "", "", ""],
      ["", "", "", "", ""],
      ["", "", "", "", ""],
    ]);

    // Clear localStorage
    saveToLocalStorage.clearAll();
  };

  // Show machine validation screen if not authorized
  if (isMachineValid === false) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-900 to-purple-900 flex items-center justify-center p-6">
        <div className="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-xl p-8 text-white text-center">
          <div className="w-20 h-20 mx-auto mb-6 bg-red-500 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <h1 className="text-4xl font-bold mb-4">Access Denied</h1>
          <p className="text-white/80 mb-6">
            {machineValidationError ||
              "This machine is not authorized to run this application"}
          </p>

          <p className="text-2xl text-white/50 mt-4">
            Contact 0945 98 49 02 to authorize this machine
          </p>
        </div>
      </div>
    );
  }

  // Show loading screen while validating
  if (isMachineValid === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Validating Machine</h2>
          <p className="text-white/70">Checking authorization...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="App relative">
      {/* Bingo Game Layout */}
      {!showIntroVideo && !showGameHistory && (
        <div className="bingo-container">
          {/* Winning Pattern */}
          <div className="bingo-pattern w-[20vw]">
            <div
              className={cn(
                "flex h-full flex-col rounded-xl border border-slate-600/30 bg-gradient-to-br from-slate-800 to-slate-900  shadow-2xl",
              )}
            >
              {/* Header */}
              <div className=" text-center">
                <h2 className="text-2xl font-bold text-white flex justify-center gap-1">
                  {bingoLetters.map((letter, index) => (
                    <div
                      key={`letter-${index}`}
                      className={cn(
                        "flex w-[6.5vh] h-[6.5vh] items-center justify-center rounded-full py-2 text-2xl font-bold",
                        bingoColors[index].color,
                        bingoColors[index].bg,
                      )}
                    >
                      {letter}
                    </div>
                  ))}
                </h2>
              </div>
              {/* Pattern Grid */}
              <div className="flex-1  rounded-lg  flex items-center justify-center">
                <div className="grid grid-cols-5 gap-1">
                  {patternGrid &&
                    patternGrid.map((row, rowIndex) =>
                      row.map((cellState, colIndex) => (
                        <motion.div
                          onClick={() => handleLeftClick(rowIndex, colIndex)}
                          onContextMenu={(e) =>
                            handleRightClick(e, rowIndex, colIndex)
                          }
                          key={`${rowIndex}-${colIndex}`}
                          className={cn(
                            "w-[6.5vh] h-[6.5vh] flex rounded-full items-center justify-center  border-2",
                            cellState === "yellow"
                              ? "border-yellow-300 bg-gradient-to-br from-yellow-400 to-yellow-600"
                              : cellState === "green"
                                ? "border-green-300 bg-gradient-to-br from-green-400 to-green-600"
                                : "border-gray-600 bg-gray-700/50",
                          )}
                        >
                          {cellState && rowIndex === 2 && colIndex === 2 ? (
                            <span className="uppercase text-[1.8vh] text-white">
                              Free
                            </span>
                          ) : (
                            <div className="text-xl font-bold text-black"></div>
                          )}
                        </motion.div>
                      )),
                    )}
                </div>
              </div>
            </div>
          </div>

          <div className="bingo-drawing  justify-start flex  ">
            <div className="text-white text-center text-6xl w-1/12  grid ">
              <span className="border-b-4  w-full">
                {selectedNumbers.length}
              </span>{" "}
              75
            </div>
            <div
              className={cn(
                "relative flex h-full flex-col items-center w-8/12 justify-center",
              )}
            >
              {/* Main Circle Container */}
              <motion.div
                className="border-gradient-to-r relative h-[35vh] w-[35vh] bg-blue-700 rounded-full border-8 border-yellow-400 shadow-2xl"
                style={{
                  boxShadow:
                    "inset 0 0 50px rgba(255, 255, 0, 0.3), 0 20px 100px rgba(0, 0, 0, 0.5)",
                }}
                animate={{
                  background: [
                    "radial-gradient(circle at 50% 50%, rgba(255, 255, 0, 0.8) 0%, rgba(255, 255, 0, 0.4) 30%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 1) 100%)",
                    "radial-gradient(circle at 50% 50%, rgba(255, 255, 0, 0.3) 0%, rgba(255, 255, 0, 0.1) 20%, rgba(0, 0, 0, 0.6) 60%, rgba(0, 0, 0, 1) 100%)",
                    "radial-gradient(circle at 50% 50%, rgba(255, 255, 0, 0.8) 0%, rgba(255, 255, 0, 0.4) 30%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 1) 100%)",
                  ],
                  boxShadow: [
                    "inset 0 0 50px rgba(255, 255, 0, 0.3), 0 20px 100px rgba(0, 0, 0, 0.5)",
                    "inset 0 0 80px rgba(255, 255, 0, 0.6), 0 20px 100px rgba(0, 0, 0, 0.5)",
                    "inset 0 0 50px rgba(255, 255, 0, 0.3), 0 20px 100px rgba(0, 0, 0, 0.5)",
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                {/* Current Ball in Center */}
                {currentBall ? (
                  <motion.div
                    key={`current-${currentBall}`}
                    className={cn(
                      "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform",
                      "flex h-[40] w-40 flex-col items-center justify-center rounded-full",
                      "font-bold text-white relative overflow-hidden",
                    )}
                    style={{
                      background: `radial-gradient(ellipse 60px 40px at 35% 25%, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.3) 20%, ${getBallColor(currentBall).includes('bg-blue') ? '#3b82f6' : getBallColor(currentBall).includes('bg-purple') ? '#8b5cf6' : getBallColor(currentBall).includes('bg-red') ? '#ef4444' : getBallColor(currentBall).includes('bg-green') ? '#10b981' : '#f59e0b'} 40%, ${getBallColor(currentBall).includes('bg-blue') ? '#1d4ed8' : getBallColor(currentBall).includes('bg-purple') ? '#6d28d9' : getBallColor(currentBall).includes('bg-red') ? '#dc2626' : getBallColor(currentBall).includes('bg-green') ? '#059669' : '#d97706'} 80%, rgba(0, 0, 0, 0.4) 100%)`,
                      boxShadow: `
                        /* Outer shadows for depth */
                        0 15px 30px rgba(0, 0, 0, 0.6),
                        0 8px 15px rgba(0, 0, 0, 0.4),
                        /* Inner shadows for sphere effect */
                        inset -10px -10px 30px rgba(0, 0, 0, 0.3),
                        inset 10px 10px 30px rgba(255, 255, 255, 0.1)
                      `,
                      border: '2px solid rgba(255, 255, 255, 0.3)',
                    }}
                    initial={{ scale: 0, rotate: 0 }}
                    animate={
                      selectedNumbers[0]
                        ? {
                            scale: [0, 1.4, 1],
                            rotate: [0, 360, 720],
                            boxShadow: [
                              `
                                0 15px 30px rgba(0, 0, 0, 0.6),
                                0 8px 15px rgba(0, 0, 0, 0.4),
                                inset -10px -10px 30px rgba(0, 0, 0, 0.3),
                                inset 10px 10px 30px rgba(255, 255, 255, 0.1)
                              `,
                              `
                                0 20px 40px rgba(255, 255, 255, 0.3),
                                0 15px 30px rgba(0, 0, 0, 0.6),
                                0 8px 15px rgba(0, 0, 0, 0.4),
                                inset -10px -10px 30px rgba(0, 0, 0, 0.3),
                                inset 10px 10px 30px rgba(255, 255, 255, 0.2)
                              `,
                              `
                                0 15px 30px rgba(0, 0, 0, 0.6),
                                0 8px 15px rgba(0, 0, 0, 0.4),
                                inset -10px -10px 30px rgba(0, 0, 0, 0.3),
                                inset 10px 10px 30px rgba(255, 255, 255, 0.1)
                              `,
                            ],
                            transition: { duration: 2.5, ease: "easeOut" },
                          }
                        : {
                            scale: 1,
                            rotate: 0,
                            boxShadow: `
                              0 15px 30px rgba(0, 0, 0, 0.6),
                              0 8px 15px rgba(0, 0, 0, 0.4),
                              inset -10px -10px 30px rgba(0, 0, 0, 0.3),
                              inset 10px 10px 30px rgba(255, 255, 255, 0.1)
                            `,
                          }
                    }
                  >
                    {/* Highlight overlay for sphere effect */}
                    <div
                      className="absolute inset-0 rounded-full pointer-events-none"
                      style={{
                        background: "radial-gradient(ellipse 50px 30px at 30% 20%, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 40%, transparent 70%)",
                      }}
                    />
                    {/* Rim light effect */}
                    <div
                      className="absolute inset-0 rounded-full pointer-events-none"
                      style={{
                        background: "radial-gradient(circle at 50% 50%, transparent 75%, rgba(255, 255, 255, 0.3) 90%, rgba(255, 255, 255, 0.1) 100%)",
                      }}
                    />
                    {/* Content */}
                    <div className="relative z-10 flex flex-col items-center justify-center">
                      <p className="!text-[10vh] font-bold drop-shadow-lg">
                        {getBallLetter(currentBall)}
                      </p>
                      <p className="font-bold !text-[4vh] drop-shadow-lg">{currentBall}</p>
                    </div>
                  </motion.div>
                ) : (
                  <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform text-center">
                    <div className="flex h-40 w-40 items-center bg-white justify-center rounded-full border-4 border-dashed border-white/30">
                      <div className="text-xl font-bold text-black  text-[12vh] ">
                        {selectedNumbers.length ? (
                          selectedNumbers[selectedNumbers.length - 1]
                        ) : (
                          <div className="text-[6vh] uppercase">Free</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Recent Balls Around the Circle - Max 8 balls with circular animation */}
                {selectedNumbers
                  .slice(-8)
                  .reverse()
                  .map((ball, index) => {
                    const angle = index * 45 - 90; // Start from top, 45 degrees apart
                    const radius = 150; // Distance from center
                    const x = Math.cos((angle * Math.PI) / 180) * radius;
                    const y = Math.sin((angle * Math.PI) / 180) * radius;

                    const indexFinder = bingoColors.findIndex(
                      (color) =>
                        color.letter.toLowerCase() ===
                        getBallLetter(ball).toLowerCase(),
                    );

                    // Calculate if this is a newly added ball (index 0 is the most recent)
                    const isNewBall = index === 0;

                    // For new balls, animate from center to position
                    // For existing balls, animate them rotating clockwise to make room
                    const fromAngle = isNewBall ? -90 : (index - 1) * 45 - 90; // Previous position
                    const fromRadius = isNewBall ? 0 : 150; // Start from center if new
                    const fromX = Math.cos((fromAngle * Math.PI) / 180) * fromRadius;
                    const fromY = Math.sin((fromAngle * Math.PI) / 180) * fromRadius;


                    return (
                      <motion.div
                        key={`recent-${ball}`}
                        className={cn(
                          "absolute flex h-20 w-20 flex-col items-center justify-center rounded-full",
                          "border-3 border-white/70 text-sm font-bold text-white shadow-lg",
                          getBallColor(ball),
                        )}
                        style={{
                          left: "calc(50% - 40px)",
                          top: "calc(50% - 40px)",
                        }}
                        initial={{
                          x: isNewBall ? 0 : fromX,
                          y: isNewBall ? 0 : fromY,
                          scale: isNewBall ? 0 : 1,
                          opacity: isNewBall ? 0 : 1,
                          rotate: isNewBall ? 0 : fromAngle + 90,
                        }}
                        animate={{
                          x: x,
                          y: y,
                          scale: 1,
                          opacity: 1,
                          // rotate: angle + 90,
                        }}
                        transition={{
                          type: "spring",
                          stiffness: 150,
                          damping: 25,
                          duration: 1.2,
                          delay: isNewBall ? 0.2 : index * 0.08, // Stagger existing balls
                        }}
                        layout
                      >
                        <span
                          className={cn(
                            `text-[2.5vh] text-center font-bold border-2  rounded-full h-[4vh] mt-[1vh] w-[2vw]`,
                          )}
                        >
                          {getBallLetter(ball)}
                        </span>
                        <span className="text-[4vh] -mt-[1vh] font-bold">
                          {ball}
                        </span>
                      </motion.div>
                    );
                  })}
              </motion.div>
            </div>
            <div className="text-center w-3/12 pr-6">
              <div className="text-[6vh] text-white uppercase tracking-wide mb-1">
                ደራሽ
              </div>
              <div className="flex justify-center">
                <motion.div
                  className=" grid justify-center max-w-[5vw]  font-bold text-yellow-400 mb-1 border-0"
                  animate={{
                    scale: [1, 1.05, 1],
                    textShadow: [
                      "0 0 10px rgba(255, 255, 0, 0.5)",
                      "0 0 20px rgba(255, 255, 0, 0.8)",
                      "0 0 10px rgba(255, 255, 0, 0.5)",
                    ],
                    transition: { duration: 2, repeat: Infinity },
                  }}
                >
                  <input
                    type="text"
                    className=" text-[5.5vh] max-w-[25vw] text-center"
                    value={maxWin2}
                    onChange={(value) => handleMaxwin(value)}
                  />
                </motion.div>
              </div>
            </div>
          </div>

          {/* Game Info Bar */}
          <div className="bingo-info">
            {checkResult && checkResult.isValid ? (
              /* Board Display Mode - Side by side layout */
              <div className="flex gap-4">
                {/* Left side - Controls */}
                <div className="flex flex-col mr-20 gap-2">
                  <div className="text-center">
                    <div className="gap-1 grid grid-flow-row grid-cols-1">
                      <div>
                        <input
                          type="text"
                          value={boardId}
                          onChange={(e) => setBoardId(e.target.value)}
                          onKeyDown={handleBoardIdKeyDown}
                          placeholder="Board ID"
                          className={cn(
                            " py-2 bg-white/10 border font-extrabold rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 text-[3.1vh] font-serif w-[6vw] font- text-center",
                            blockedBoards.has(parseInt(boardId))
                              ? "border-orange-500 focus:ring-orange-500 focus:border-orange-500"
                              : "border-white/20 focus:ring-blue-500 focus:border-transparent",
                          )}
                          disabled={isCheckingBoard}
                        />
                      </div>
                      <div className="w-[6vw]">
                        <motion.button
                          onClick={handleCheckBoard}
                          disabled={
                            !isValidBoardIndex(parseInt(boardId)) ||
                            isCheckingBoard ||
                            blockedBoards.has(parseInt(boardId))
                          }
                          className={cn(
                            "py-2 rounded-lg font-semibold text-white transition-all w-full",
                            !isValidBoardIndex(parseInt(boardId)) ||
                              isCheckingBoard ||
                              blockedBoards.has(parseInt(boardId))
                              ? "bg-gray-600 cursor-not-allowed"
                              : "bg-green-600 hover:bg-green-700 hover:scale-105",
                          )}
                          whileHover={{
                            scale:
                              !isValidBoardIndex(parseInt(boardId)) ||
                              isCheckingBoard ||
                              blockedBoards.has(parseInt(boardId))
                                ? 1
                                : 1.05,
                          }}
                          whileTap={{
                            scale:
                              !isValidBoardIndex(parseInt(boardId)) ||
                              isCheckingBoard ||
                              blockedBoards.has(parseInt(boardId))
                                ? 1
                                : 0.95,
                          }}
                        >
                          {isCheckingBoard ? (
                            <div className="flex items-center justify-center gap-2">
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                              Checking...
                            </div>
                          ) : (
                            "Check"
                          )}
                        </motion.button>
                      </div>
                      <div className="w-[6vw]">
                        {checkResult && checkResult.isValid && (
                          <motion.button
                            onClick={handleBlockBoard}
                            className="px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-semibold transition-all w-full"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            Block
                          </motion.button>
                        )}
                      </div>
                      <div className="w-[6vw]">
                        {checkResult && (
                          <motion.button
                            onClick={() => setCheckResult(null)}
                            className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-all w-full"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            Back
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right side - Board Display */}
                <motion.div
                  className={cn(
                    "bg-gradient-to-r  from-blue-900 via-blue-800 to-blue-900 rounded-xl p-4 text-white shadow-2xl border border-blue-600/30  flex flex-col justify-center",
                  )}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex-1 flex flex-col">
                    {/* Board Grid with BINGO Letters */}
                    <div className="flex-1 flex items-center justify-center">
                      <div className="flex flex-col">
                        {/* BINGO Letters Row - Aligned with columns */}
                        <div className="grid grid-cols-5 gap-1 mb-2">
                          {["B", "I", "N", "G", "O"].map((letter, index) => (
                            <div
                              key={letter}
                              className={cn(
                                "w-[6.4vh] grid h-[5vh]  items-center justify-center text-2xl font-bold rounded",
                                index === 0 && "bg-blue-600 text-white",
                                index === 1 && "bg-purple-600 text-white",
                                index === 2 && "bg-red-600 text-white",
                                index === 3 && "bg-green-600 text-white",
                                index === 4 && "bg-yellow-600 text-black",
                              )}
                            >
                              {letter}
                            </div>
                          ))}
                        </div>

                        {/* Board Numbers Grid */}
                        <div className="grid min-w-[20vw] grid-cols-5 gap-2">
                          {/* Convert column-based board to row-based display */}
                          {Array.from({ length: 5 }, (_, rowIndex) =>
                            Array.from({ length: 5 }, (_, colIndex) => {
                              // Access board data as board[col][row] since it's column-based
                              const number =
                                checkResult.board[colIndex][rowIndex];
                              const isMarked =
                                number !== 80 &&
                                checkResult.markedNumbers.includes(number);
                              const isFree = number === 80;

                              return (
                                <motion.div
                                  key={`${rowIndex}-${colIndex}`}
                                  className={cn(
                                    "w-[6.4vh] grid h-[6.7vh]  items-center justify-center text-[3.5vh] font-bold rounded-full border-2",
                                    isFree
                                      ? "bg-gradient-to-br from-green-400 to-green-600 text-black border-green-300"
                                      : isMarked
                                        ? "bg-gradient-to-br from-green-500 to-green-700 text-white border-green-300"
                                        : "bg-white/70 text-black/50 border-gray-300",
                                  )}
                                  initial={{ scale: 0.8, opacity: 0 }}
                                  animate={{ scale: 1, opacity: 1 }}
                                  transition={{
                                    delay: (rowIndex + colIndex) * 0.05,
                                  }}
                                >
                                  {isFree ? (
                                    <div className="text-center">
                                      <div className="text-lg">★</div>
                                      <div className="text-xs font-bold">
                                        FREE
                                      </div>
                                    </div>
                                  ) : (
                                    <span>{number}</span>
                                  )}
                                </motion.div>
                              );
                            }),
                          ).flat()}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            ) : (
              /* Normal Mode - Single column layout */
              <div className="flex flex-col gap-4">
                {/* Controls Section */}


                {/* Info/Error Section */}
                <motion.div
                  className={cn(
                    "bg-gradient-to-r max-w-[25vw] min-w-[25vw] from-blue-900 via-blue-800 to-blue-900 rounded-xl p-4 text-white shadow-2xl border border-blue-600/30 h-full flex flex-col justify-center",
                  )}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {checkResult && !checkResult.isValid ? (
                    /* Error Display Mode */
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-red-400 text-lg font-bold mb-2">
                          ❌ Error
                        </div>
                        <div className="text-red-300">{checkResult.error}</div>
                      </div>
                    </div>
                  ) : (
                    /* Normal Info Mode */
                    <span className="">
                      {/* Game Title */}
                      <div className="text-center">
                  <div className="gap-1 grid w-full grid-flow-row grid-cols-2">
                    <div>
                      <input
                        type="text"
                        value={boardId}
                        onChange={(e) => setBoardId(e.target.value)}
                        onKeyDown={handleBoardIdKeyDown}
                        placeholder="Board ID"
                        className={cn(
                          " py-2 bg-white/10 border font-extrabold rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 text-[3.5vh] font-serif h-[6.4vh] w-[15.5vw] font- text-center",
                          blockedBoards.has(parseInt(boardId))
                            ? "border-orange-500 focus:ring-orange-500 focus:border-orange-500"
                            : "border-white/20 focus:ring-blue-500 focus:border-transparent",
                        )}
                        disabled={isCheckingBoard}
                      />
                    </div>
                    <div className="grid h-full justify-end items-center">
                      <motion.button
                        onClick={handleCheckBoard}
                        disabled={
                          !isValidBoardIndex(parseInt(boardId)) ||
                          isCheckingBoard ||
                          blockedBoards.has(parseInt(boardId))
                        }
                        className={cn(
                          "px-4 py-2 text-[3vh] rounded-lg font-semibold text-white transition-all",
                          !isValidBoardIndex(parseInt(boardId)) ||
                          isCheckingBoard ||
                          blockedBoards.has(parseInt(boardId))
                            ? "bg-gray-600 cursor-not-allowed"
                            : "bg-green-600 hover:bg-green-700 hover:scale-105",
                        )}
                        whileHover={{
                          scale:
                            !isValidBoardIndex(parseInt(boardId)) ||
                            isCheckingBoard ||
                            blockedBoards.has(parseInt(boardId))
                              ? 1
                              : 1.05,
                        }}
                        whileTap={{
                          scale:
                            !isValidBoardIndex(parseInt(boardId)) ||
                            isCheckingBoard ||
                            blockedBoards.has(parseInt(boardId))
                              ? 1
                              : 0.95,
                        }}
                      >
                        {isCheckingBoard ? (
                          <div className="flex items-center justify-center gap-2">
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                            Checking...
                          </div>
                        ) : (
                          "Check"
                        )}
                      </motion.button>
                    </div>
                    <div>
                      {checkResult && checkResult.isValid && (
                        <motion.button
                          onClick={handleBlockBoard}
                          className="px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-semibold transition-all"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Block
                        </motion.button>
                      )}
                    </div>
                    <div>
                      {checkResult && (
                        <motion.button
                          onClick={() => setCheckResult(null)}
                          className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-all"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Back
                        </motion.button>
                      )}
                    </div>
                  </div>
                </div>
                      <div className="text-center mb-4 text-[3.8vh] ">
                        0945984902
                        <div className="text-[4.5vh] uppercase font-bold text-yellow-400 mb-1 tracking-wider">
                          Bana International Bingo
                        </div>
                      </div>

                      {/* Clear Button */}
                      <div className="text-center">
                        <div className="flex justify-center">
                          <div className="items-center justify-center bg-gradient-to-r flex gap-10 from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-2 px-4 rounded-lg shadow-lg border border-red-500/30 transition-all duration-200">
                            <Dialog
                              open={isClearDialogOpen}
                              onOpenChange={setIsClearDialogOpen}
                            >
                              <DialogContent className="bg-slate-800 border-slate-600">
                                <DialogHeader>
                                  <DialogTitle className="text-yellow-400">
                                    Confirm Clear
                                  </DialogTitle>
                                  <DialogDescription className="text-slate-300">
                                    Are you sure you want to reset all game data?
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="flex gap-4 justify-end mt-4">
                                  <Button
                                    className="bg-green-400 text-black text-[2.4vh]"
                                    variant={"outline"}
                                    onClick={() => setIsClearDialogOpen(false)}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    onClick={handleClearConfirm}
                                    className="bg-red-700 text-white text-[2.4vh]"
                                    variant={"destructive"}
                                  >
                                    Confirm
                                  </Button>
                                </div>
                              </DialogContent>
                            </Dialog>

                            <motion.button
                              onClick={() => setIsClearDialogOpen(true)}
                              className=""
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <span className="text-[1.2vw]">Clear</span>
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </span>
                  )}
                </motion.div>
              </div>
            )}
          </div>

          {/* Number Grid */}
          <div className="bingo-grid ">
            <div
              className={cn(
                "flex h-full flex-col rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 p-2  shadow-2xl",
              )}
            >
              {/* Grid with BINGO letters on the left */}
              <div
                className="grid min-h-0  flex-1 grid-cols-16 gap-1"
                // variants={plateVariants}
              >
                {/* Create rows with BINGO letters */}
                {Array.from({ length: rows }, (_, rowIndex) => {
                  const rowElements = [];

                  // Add BINGO letter for this row
                  rowElements.push(
                    <div
                      key={`letter-${rowIndex}`}
                      className={cn(
                        "flex h-[9.2vh] w-[5vw] items-center justify-center rounded-lg text-[4vh] font-bold",
                        bingoColors[rowIndex].color,
                        bingoColors[rowIndex].bg,
                      )}
                    >
                      {bingoLetters[rowIndex]}
                    </div>,
                  );

                  // Add numbers for this row
                  for (let col = 0; col < cols; col++) {
                    const number = rowIndex * cols + col + 1;
                    const delay = (rowIndex + col) * 0.01;

                    rowElements.push(
                      <div
                        key={`${rowIndex}-${col}`}
                        // variants={numberEntryVariants}
                        className="select-none "
                        onClick={() => handleClick(number)}
                      >
                        <BingoNumber
                          num={number}
                          isDrawn={drawnSet.has(number)}
                          isSelected={selectedSet.has(number)}
                          isLastDrawn={
                            number ===
                            selectedNumbers[selectedNumbers.length - 1]
                          }
                        />
                      </div>,
                    );
                  }

                  return rowElements;
                }).flat()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Block Confirmation Dialog */}
      <Dialog open={isBlockDialogOpen} onOpenChange={setIsBlockDialogOpen}>
        <DialogContent className="bg-slate-800 border-slate-600">
          <DialogHeader>
            <DialogTitle className="text-orange-400">Block Board</DialogTitle>
            <DialogDescription className="text-slate-300">
              Are you sure you want to block board {checkResult?.boardId}? This
              board will not be able to be checked again until the game
              restarts.
              <br />
              <br />
              <span className="text-sm text-slate-400">
                Press Enter to confirm or Escape to cancel.
              </span>
            </DialogDescription>
          </DialogHeader>
          <div className="flex gap-4 justify-end mt-4">
            <Button
              className="bg-green-400 text-black text-[2.4vh]"
              variant={"outline"}
              onClick={() => setIsBlockDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBlockConfirm}
              className="bg-orange-700 text-white text-[2.4vh]"
              variant={"destructive"}
            >
              Block Board
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

const BingoNumber: React.FC<BingoNumberProps> = ({
  num,
  isDrawn,
  isSelected,
  isLastDrawn,
}) => {
  // Determine BINGO column color
  const getColumnColor = (number: number) => {
    if (number >= 1 && number <= 15) return "bg-blue-500"; // B
    if (number >= 16 && number <= 30) return "bg-purple-500"; // I
    if (number >= 31 && number <= 45) return "bg-red-500"; // N
    if (number >= 46 && number <= 60) return "bg-green-500"; // G
    if (number >= 61 && number <= 75) return "bg-yellow-500"; // O
    return "bg-gray-500";
  };

  return (
    <motion.div
      className={cn(
        "relative flex font-extrabold !z-50  h-[9.2vh] items-center justify-center rounded text-[6vh] cursor-pointer",
        isDrawn
          ? getColumnColor(num)
          : isSelected
            ? getColumnColor(num) + " text-white"
            : "bg-gray-700 text-gray-300/30 hover:bg-gray-600",
      )}
    >
      <span className="relative z-10">{num}</span>
      {(isDrawn || isSelected) && (
        <motion.div
          className={cn(
            "absolute inset-0 rounded",
            isDrawn ? "bg-white/20" : "bg-white/10",
          )}
        />
      )}
    </motion.div>
  );
};
