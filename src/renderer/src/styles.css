@import "tailwindcss";

/* Custom grid for Bingo 75 layout */
@layer utilities {
  .grid-cols-15 {
    grid-template-columns: repeat(15, minmax(0, 1fr));
  }
  .grid-cols-16 {
    grid-template-columns: auto repeat(15, minmax(0, 1fr));
  }
}

@font-face {
  font-family: "Eurostib";
  src: url("./assets/fonts/eurostib.ttf");
}
@font-face {
  font-family: "Eurostib-Pro";
  src: url("./assets/fonts/eurostib-pro.otf");
}
@font-face {
  font-family: "Goodtimes";
  src: url("./assets/fonts/good-times.otf");
}

@font-face {
  font-family: "Gunar";
  src: url("./assets/fonts/gunar-extrabold.ttf");
}

@layer base {
  #root,
  body,
  html {
    height: 100%;
  }
  body {
    font-family: "Eurostib", sans-serif !important;
  }
}

.Eurostib {
  font-family: "Eurostib";
}

.Eurostib-Pro {
  font-family: "Eurostib-Pro";
}

.Goodtimes {
  font-family: "Goodtimes";
}

.Gunar {
  font-family: "Gunar";
}

.NumberAppContainer {
  display: flex;
  width: 100%;
}

.App {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /*cursor: none !important;*/
  /* background: url("./assets/images/vidbg.png") no-repeat center center fixed; */
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.text-error {
  color: #ec3118;
}

.text-shadow {
  text-shadow: -3px -2px 8px black;
}

.animated-number {
  position: absolute !important;
  overflow: visible !important;
  transform-origin: center center !important;
  z-index: 9999 !important;
}

.drawTextImage {
  height: 8vh;
  object-fit: cover;
}

.default-draw {
  background-image: url("./assets/images/default-draw.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: rgb(235, 0, 0) !important;
}

.head-draw {
  background-image: url("./assets/images/head-draw.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: #000 !important;
}

.tail-draw {
  background-image: url("./assets/images/tail-draw.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: #000 !important;
}

.leftSideBar {
  position: relative;
  flex: 0 58%;
  height: 100vh;
  padding: 0.2vw 1.2vw 0 3.5vw;
}

.info {
  height: 100vh;
  flex: 0 42%;
  color: #fff;
  padding-top: 4.2vh;
  background-image: linear-gradient(
    to bottom,
    #7e190e,
    #63140b,
    #430d08,
    #2a0804,
    #060100
  );
}

.drawingVideoContainer {
  position: relative;
  flex: 0 39.77%;
  height: 100vh;
  position: relative;
  background: url("./assets/images/last-frame.jpg") no-repeat center center
    fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  z-index: 50;
  overflow: hidden;
}

.drawNumberCounter {
  font-family: "Eurostib";
  color: white;
  position: absolute;
  top: 7%;
  right: 0;
  font-weight: bold;
}

.introvideo {
  background: url("./assets/images/intro-poster.jpg") no-repeat center center
    fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.connecting {
  position: relative;
  height: 100%;
  width: 100%;
  background: url("./assets/images/bg.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.history {
  width: 100vw;
  height: 100vh;
  background: url("./assets/images/historybg.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  overflow: hidden;
}

.history-head {
  background-image: linear-gradient(
    to bottom,
    #ffff37,
    #ffff37,
    #ffff37,
    #ffff37,
    #d7e411,
    #d7e411
  );
}
.history-tail {
  background-image: linear-gradient(
    to bottom,
    #f8b438,
    #f8b438,
    #f8b438,
    #f8b438,
    #d07b14,
    #d07b14
  );
}

.history-entry {
  position: relative;
  padding: 0.5vh 0;
  width: 100%;
}

.history-entry::after,
.history-entry:first-child::before {
  opacity: 50%;
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 98%;
  height: 0.3vh !important;
  background: linear-gradient(
    to right,
    rgba(254, 241, 9, 0),
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(254, 148, 13, 0)
  );
  pointer-events: none;
  z-index: 5;
}

.history-entry::after {
  bottom: 0;
}

.history-entry:first-child::before {
  top: 0;
}

.middle-line {
  position: relative;
  display: grid;
  grid-template-columns: repeat(20, 1fr);
  gap: 0.1vw;
}

.middle-line > div:nth-child(10) {
  position: relative;
  margin-right: 1.25vh;
}

.middle-line .bg-red-500 {
  position: absolute;
  right: -1vh;
  top: -15%;
  height: 130%;
  width: 0.6vh !important;
  background: rgba(243, 243, 243, 0.8);
  z-index: 6;
}

.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 0;
  }
  60% {
    opacity: 0;
  }
  90% {
    opacity: 1;
  }
}

/* Bingo-specific styles */
.bingo-container {
  display: grid;
  grid-template-areas:
    "pattern drawing info"
    "grid grid grid";
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: 45vh 55vh;
  height: 100vh;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  min-height: 100vh;
}

.bingo-info {
  grid-area: info;
}

.bingo-drawing {
  grid-area: drawing;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bingo-grid {
  grid-area: grid;
  height: 50vh;
}

.bingo-pattern {
  grid-area: pattern;
  overflow: hidden;
}

/* Professional TV display optimizations */
@media (min-width: 1920px) {
  .bingo-container {
    /* padding: 2rem; */
    gap: 2rem;
  }
}

/* Ensure text is readable on large displays */
.bingo-container * {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bingo-ball {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.bingo-ball-b {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.bingo-ball-i {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.bingo-ball-n {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.bingo-ball-g {
  background: linear-gradient(135deg, #10b981, #059669);
}

.bingo-ball-o {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}
